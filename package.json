{"name": "asset_mwg", "version": "2.0.20", "version-live": "2.0.19", "private": true, "appName": "XWORK", "moduleName": "Ticket", "featureName": "AssetMWG", "scripts": {"test": "jest", "lint": "eslint ./src", "lint-fix": "eslint ./src --fix", "prettier": "prettier --write ./src", "format": "prettier --check ./src", "android": "react-native run-android", "ios": "react-native run-ios", "start": "STANDALONE=1 react-native webpack-start --host 127.0.0.1 --port 9000", "build": "bash buildBundle.sh", "prepare": "husky install"}, "dependencies": {"@apollo/client": "3.3.16", "@callstack-mwg/repack": "^4.4.1", "@codler/react-native-keyboard-aware-scroll-view": "2.0.1", "@mwg-kits/common": "^0.0.4", "@mwg-kits/components": "0.1.25", "@mwg-kits/core": "^0.1.18", "@mwg-sdk/styles": "^1.0.5", "@react-native-async-storage/async-storage": "^1.17.10", "@react-native-community/clipboard": "^1.5.1", "@react-navigation/material-top-tabs": "^6.6.2", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.9", "@react-navigation/stack": "^6.3.29", "@sdk/api": "0.0.1-beta9", "apollo-link-timeout": "4.0.0", "apollo3-cache-persist": "0.15.0", "eslint-plugin-import": "^2.27.5", "lottie-react-native": "6.3.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-animatable": "^1.4.0", "react-native-background-timer": "2.4.1", "react-native-blob-util": "^0.19.8", "react-native-config": "1.5.0", "react-native-device-info": "^10.3.0", "react-native-gesture-handler": "2.9.0", "react-native-keyboard-aware-scroll-view": "^0.9.1", "react-native-permissions": "^3.8.0", "react-native-reanimated": "2.17.0", "react-native-safe-area-context": "4.10.1", "react-native-safe-area-view": "^1.1.1", "react-native-screens": "3.18.0", "react-native-xwork": "git+https://sourceapp.tgdd.vn/SuperXwork/react-native-xwork.git#develop", "react-redux": "^8.0.4", "redux": "^4.2.0", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.1", "rn-background-timer-id": "2.4.3", "ts-loader": "^9.4.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-decorators": "^7.23.9", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@mwg/eslint-config-react": "https://sourceapp.tgdd.vn/appplugins/eslint-config.git", "@mwg/prettier-config": "https://sourceapp.tgdd.vn/appplugins/prettier-config.git", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.12", "@types/react": "^18.3.3", "@types/react-native": "^0.73.0", "@types/react-native-background-timer": "^2.0.2", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.3.0", "@types/redux-logger": "^3.0.13", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "babel-loader": "^9.1.2", "babel-plugin-module-resolver": "^4.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^10.0.0", "eslint-plugin-jsx-a11y": "^6.7.1", "husky": ">=6", "jest": "^26.6.3", "lint-staged": ">=10", "metro-react-native-babel-preset": "0.72.3", "react-test-renderer": "18.1.0", "redux-devtools-extension": "^2.13.9", "terser-webpack-plugin": "^5.3.6", "typescript": "^5.5.4", "webpack": "^5.91.0"}, "jest": {"preset": "react-native"}, "lint-staged": {"*.js": "eslint --cache --fix ./src", "*.{js,css,md}": "prettier --write ./src"}}