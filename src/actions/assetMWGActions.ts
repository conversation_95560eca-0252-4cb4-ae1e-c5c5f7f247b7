import {
    IListAssetHistoryRequest,
    IListAssetHistoryResponse,
    IListAssetRequest,
    IListAssetResponse,
    initError
} from '@types'
import { getListAssetHistoryService, getListAssetsService } from '../services'

export const GET_LIST_ASSETS_SUCCESS = 'GET_LIST_ASSETS_SUCCESS'
export const GET_LIST_ASSETS_FAILURE = 'GET_LIST_ASSETS_FAILURE'
export const GET_LIST_ASSETS_PENDING = 'GET_LIST_ASSETS_PENDING'

export const GET_LIST_ASSET_HISTORY_SUCCESS = 'GET_LIST_ASSET_HISTORY_SUCCESS'
export const GET_LIST_ASSET_HISTORY_FAILURE = 'GET_LIST_ASSET_HISTORY_FAILURE'
export const GET_LIST_ASSET_HISTORY_PENDING = 'GET_LIST_ASSET_HISTORY_PENDING'

export const getListAssets = (
    request: IListAssetRequest,
    isLoading: boolean = true
) => {
    return async (dispatch: (action: any) => void) => {
        if (isLoading) {
            dispatch({
                type: GET_LIST_ASSETS_PENDING
            })
        }

        try {
            const requestData = {
                pageRequest: {
                    search: request.search,
                    iDisplayStart: request.iDisplayStart,
                    iDisplayLength: request.iDisplayLength
                }
            }

            const response: IListAssetResponse = await getListAssetsService(
                requestData
            )

            dispatch({
                type: GET_LIST_ASSETS_SUCCESS,
                payload: response,
                meta: {
                    iDisplayStart: request.iDisplayStart,
                    iDisplayLength: request.iDisplayLength,
                    search: request.search
                }
            })

            return response
        } catch (error) {
            dispatch({
                type: GET_LIST_ASSETS_FAILURE,
                payload: error || initError
            })

            return null
        }
    }
}

export const getListAssetHistory = (
    request: IListAssetHistoryRequest,
    isLoading: boolean = true
) => {
    return async (dispatch: (action: any) => void) => {
        if (isLoading) {
            dispatch({
                type: GET_LIST_ASSET_HISTORY_PENDING
            })
        }

        try {
            const requestData = {
                pageRequest: {
                    assetCode: request.assetCode,
                    iDisplayStart: request.iDisplayStart,
                    iDisplayLength: request.iDisplayLength
                }
            }

            const response: IListAssetHistoryResponse =
                await getListAssetHistoryService(requestData)

            dispatch({
                type: GET_LIST_ASSET_HISTORY_SUCCESS,
                payload: response,
                meta: {
                    iDisplayStart: request.iDisplayStart,
                    iDisplayLength: request.iDisplayLength,
                    assetCode: request.assetCode
                }
            })

            return response
        } catch (error) {
            dispatch({
                type: GET_LIST_ASSET_HISTORY_FAILURE,
                payload: error || initError
            })

            return null
        }
    }
}
