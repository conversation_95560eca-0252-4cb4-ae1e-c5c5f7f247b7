import { IListAssetHistoryRequest, IListAssetHistoryResponse } from './types'
import { IListAssetRequest, IListAssetResponse } from '@types'
import { apiBase, METHOD } from '@sdk/api'
import { GET_LIST_ASSET_HISTORY, GET_LIST_ASSETS } from '../api'

export const getListAssetsService = async (
    request: IListAssetRequest | { pageRequest: IListAssetRequest }
) => {
    return new Promise<IListAssetResponse>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                GET_LIST_ASSETS,
                METHOD.POST,
                request
            )

            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error) {
            reject(error)
        }
    })
}

export const getListAssetHistoryService = async (
    request:
        | IListAssetHistoryRequest
        | { pageRequest: IListAssetHistoryRequest }
) => {
    return new Promise<IListAssetHistoryResponse>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                GET_LIST_ASSET_HISTORY,
                METHOD.POST,
                request
            )

            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error) {
            reject(error)
        }
    })
}
