export interface ICommonState {
    isDoneSetProps: boolean
    navigation?: any
    DateTimePickerModal: any
    Toast: any
    typeCode: string
    name: string
}

export type CommonActionTypes = { type: string; payload: any }

export interface IError {
    code: number
    error: string
    errorReason: string
    errorServiceReason: string
}

export interface IStandardReducer {
    loading: boolean
    error: IError
    data: any
}

export const initError: IError = {
    code: -1,
    error: '',
    errorReason: '',
    errorServiceReason: ''
}

export interface IListAssetRequest {
    search: string
    iDisplayStart: number
    iDisplayLength: number
}

export interface IListAssetResponse {
    draw: number
    recordsTotal: number
    recordsFiltered: number
    data: IAsset[]
}

export interface IAsset {
    assetCode: string
    assetName: string
    assetDetails: string
    assetGroup: IAssetGroup
    assetLocation: IAssetLocation
    description: string
    entryDate: string
    id: number
    createTime: string
    status: string
}

export interface IAssetGroup {
    groupCode: string
    groupName: string
    id: number
    createTime: string
    updateTime: string
    status: string
}

export interface IAssetLocation {
    locationCode: string
    locationName: string
    id: number
    createTime: string
    updateTime: string
    status: string
}
