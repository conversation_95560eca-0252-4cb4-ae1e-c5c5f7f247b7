import { View } from 'react-native'
import React from 'react'
import { Mixins, MyWrapper } from '@react-native-xwork'
import { useSelector } from 'react-redux'
import { RootReducerType } from '../../store'
import { ListHistory } from './components'

const { translate } = (global as any).props.getTranslateConfig()

interface Props {
    navigation: any
}

const ListAssetHistory = (props: Props) => {
    const { data } = useSelector(
        (state: RootReducerType) => state.assetReducer.listAssetHistory
    )

    return (
        <MyWrapper
            navigation={props.navigation}
            componentsLeftTitle={'Tài sản trụ sở MWG'}
            isErrorIcon={{ uri: 'ic_error' }}
            buttonRetry={translate('retry')}
            wrapperContainerBaseStyle={{ flex: 1 }}>
            <View style={{ flex: 1, paddingVertical: Mixins.scale(16) }}>
                <ListHistory historyData={data?.data || []} />
            </View>
        </MyWrapper>
    )
}

export default ListAssetHistory
