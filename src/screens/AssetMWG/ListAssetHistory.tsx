import { View, Text } from 'react-native'
import React from 'react'
import { MyWrapper } from '@react-native-xwork'
import { useDispatch, useSelector } from 'react-redux'
import { RootReducerType } from '../../store'

const { translate } = (global as any).props.getTranslateConfig()

interface Props {
    navigation: any
}

const ListAssetHistory = (props: Props) => {
    const dispatch: any = useDispatch()
    const { data, loading, error } = useSelector(
        (state: RootReducerType) => state.assetReducer.listAssetHistory
    )

    console.log('firstLog data', data)

    return (
        <MyWrapper
            isSuccess={!loading && error.code === -1}
            isError={error.code !== -1}
            isLoading={loading}
            // actionRetry={fetchAssets}
            navigation={props.navigation}
            componentsLeftTitle={'Tài sản trụ sở MWG'}
            isErrorIcon={{ uri: 'ic_error' }}
            buttonRetry={translate('retry')}
            wrapperContainerBaseStyle={{ flex: 1 }}>
            <View></View>
        </MyWrapper>
    )
}

export default ListAssetHistory
