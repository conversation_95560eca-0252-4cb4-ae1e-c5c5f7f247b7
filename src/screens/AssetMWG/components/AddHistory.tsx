import { View, Text, StyleSheet } from 'react-native'
import React, { useState } from 'react'
import {
    BaseButton,
    Colors,
    Mixins,
    MyText,
    TextArea
} from '@react-native-xwork'

type Props = {}

const AddHistory = (props: Props) => {
    const [description, setDescription] = useState('')

    return (
        <View style={styles.container}>
            <View style={styles.section}>
                <MyText
                    text="Trạng thái yêu cầu"
                    category="bold.sub.title.2"
                    style={styles.label}
                />
            </View>
            <View style={styles.section}>
                <View style={styles.labelContainer}>
                    <MyText
                        text="Mô tả"
                        category="bold.sub.title.2"
                        style={styles.label}
                    />
                    <MyText
                        text="*"
                        category="bold.sub.title.2"
                        style={styles.requiredText}
                    />
                </View>
                <TextArea
                    value={description}
                    onChangeText={setDescription}
                    placeholder="Nhập thêm mô tả nhé!"
                    height={Mixins.scale(120)}
                />
            </View>
            <BaseButton text="Tạo" disabled={true} />
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        gap: Mixins.scale(20)
    },
    section: {
        gap: Mixins.scale(8)
    },
    labelContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Mixins.scale(4)
    },
    label: {
        color: Colors.TEXT_PRIMARY
    },
    requiredText: {
        color: Colors.BADGE_SOFT_RED_TEXT
    }
})

export default AddHistory
