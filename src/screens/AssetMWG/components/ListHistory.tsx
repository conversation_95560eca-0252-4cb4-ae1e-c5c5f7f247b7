import { View, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native'
import React, { useMemo, useCallback } from 'react'
import { memo } from 'react'
import { Colors, Mixins, MyText } from '@react-native-xwork'
import { IAssetHistoryDetail } from '@types'

interface ListHistoryProps {
    historyData: IAssetHistoryDetail[]
    totalRecords?: number
    isLoading?: boolean
    onLoadMore?: () => void
}

interface HistoryItemProps {
    type: string
    user: string
    description: string
    date: string
    isLast: boolean
}

const HistoryItem = memo(
    ({ type, user, description, date, isLast }: HistoryItemProps) => {
        const historyItemData = useMemo(
            () => [
                {
                    label: 'Loại:',
                    value: type,
                    valueStyle: styles.historyValue
                },
                {
                    label: 'User:',
                    value: user,
                    valueStyle: styles.historyValue
                },
                {
                    label: 'Mô tả:',
                    value: description,
                    valueStyle: styles.historyValue
                }
            ],
            [type, user, description]
        )

        return (
            <View
                style={[
                    styles.historyItem,
                    !isLast && {
                        borderBottomWidth: 1,
                        borderBottomColor: Colors.BORDER_PRIMARY
                    }
                ]}>
                {historyItemData.map((item, index) => (
                    <View key={index} style={styles.historyRow}>
                        <MyText
                            text={item.label}
                            category="regular.body.2"
                            style={styles.historyLabel}
                        />
                        <MyText
                            text={item.value}
                            category="bold.body.2"
                            style={item.valueStyle}
                        />
                    </View>
                ))}
                <MyText
                    text={date}
                    category="tag.item"
                    style={styles.historyDate}
                />
            </View>
        )
    }
)

const ListHistory = memo(({ historyData, totalRecords, isLoading, onLoadMore }: ListHistoryProps) => {
    const renderLoadMore = useCallback(() => {
        const hasMoreData = totalRecords && historyData.length < totalRecords;
        if (!hasMoreData || !onLoadMore) return null;

        return (
            <TouchableOpacity
                style={styles.loadMoreButton}
                onPress={onLoadMore}
                disabled={isLoading}>
                {isLoading ? (
                    <ActivityIndicator size="small" color={Colors.TEXT_BRAND} />
                ) : (
                    <MyText
                        text={'Xem thêm'}
                        category="button.medium"
                        style={styles.loadMoreButtonText}
                    />
                )}
            </TouchableOpacity>
        );
    }, [historyData.length, totalRecords, isLoading, onLoadMore]);

    return (
        <FlatList
            contentContainerStyle={styles.container}
            scrollEnabled={false}
            data={historyData}
            renderItem={({ item, index }) => (
                <HistoryItem
                    type={item.assetCareType.caretypeName}
                    user={item.assetCareType.caretypeCode}
                    description={item.description}
                    date={item.assetCareTime}
                    isLast={index === historyData.length - 1}
                />
            )}
            keyExtractor={(item) => item.id.toString()}
            ListEmptyComponent={() => (
                <MyText
                    text="Không có lịch sử"
                    category="bold.body.1"
                    style={styles.noHistoryText}
                />
            )}
            ListFooterComponent={renderLoadMore}
        />
    )
})

const styles = StyleSheet.create({
    container: {
        gap: Mixins.scale(10)
    },
    historyItem: {
        paddingBottom: Mixins.scale(10)
    },
    historyRow: {
        flexDirection: 'row',
        marginBottom: Mixins.scale(2),
        alignItems: 'center'
    },
    historyLabel: {
        flex: 1,
        color: Colors.TEXT_SECONDARY,
        maxWidth: Mixins.scale(100)
    },
    historyValue: {
        flex: 1,
        color: Colors.TEXT_PRIMARY,
        textAlign: 'right'
    },
    historyDate: {
        color: Colors.TEXT_DISABLE,
        textAlign: 'right'
    },
    noHistoryText: {
        color: Colors.TEXT_SECONDARY
    },
    loadMoreButton: {
        marginTop: Mixins.scale(16),
        alignSelf: 'center'
    },
    loadMoreButtonText: {
        color: Colors.TEXT_BRAND
    }
})

export default ListHistory
