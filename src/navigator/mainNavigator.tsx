import React from 'react'
import { AssetMWGScreen, ListAssetHistory } from '@screens'

const StackList = [
    {
        name: 'AssetMWG',
        component: AssetMWGScreen,
        initialParams: {},
        options: {},
    },
    {
        name: 'ListAssetHistory',
        component: ListAssetHistory,
        initialParams: {},
        options: {},
    },
]

const MainNavigator = (props: any) => {
    const { MyStack } = props
    return (
        <MyStack.Navigator
            screenOptions={{ headerShown: false }}
            initialRouteName={'AssetMWG'}>
            {StackList.map((stack) => {
                return (
                    <MyStack.Screen
                        key={stack.name}
                        name={stack.name}
                        component={stack.component}
                        initialParams={stack.initialParams}
                        options={stack.options}
                    />
                )
            })}
        </MyStack.Navigator>
    )
}

export default MainNavigator
